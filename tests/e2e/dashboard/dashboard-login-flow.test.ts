/**
 * Dashboard Login Flow E2E Test
 * 
 * This test follows the complete flow:
 * 1. Start both servers (Frontend and Backend)
 * 2. Use Puppeteer to login with test account
 * 3. Navigate to dashboard page
 * 4. Verify dashboard functionality and components
 * 5. Take screenshots for verification
 */

import { test, expect } from '@playwright/test';
import { GLOBAL_TEST_ACCOUNT, TEST_CONFIG } from '../../config/test-accounts';

test.describe('Dashboard Login Flow', () => {
  test.beforeAll(async () => {
    // Verify both servers are running
    console.log('🔍 Verifying servers are running...');
    
    // Check frontend server
    try {
      const frontendResponse = await fetch(`${TEST_CONFIG.FRONTEND_URL.replace('3001', '3000')}`);
      console.log(`✅ Frontend server status: ${frontendResponse.status}`);
    } catch (error) {
      console.error('❌ Frontend server not accessible:', error);
      throw new Error('Frontend server is not running on port 3000');
    }

    // Check backend server
    try {
      const backendResponse = await fetch(`${TEST_CONFIG.BACKEND_URL}/health`);
      console.log(`✅ Backend server status: ${backendResponse.status}`);
    } catch (error) {
      console.error('❌ Backend server not accessible:', error);
      throw new Error('Backend server is not running on port 8050');
    }

    console.log('🎉 Both servers are running successfully!');
  });

  test('Complete Login and Dashboard Flow', async ({ page }) => {
    console.log('🚀 Starting complete login and dashboard flow test...');

    // Step 1: Navigate to the application
    console.log('📍 Step 1: Navigating to login page...');
    await page.goto(`${TEST_CONFIG.FRONTEND_URL.replace('3001', '3000')}/en/login`);
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of login page
    await page.screenshot({ 
      path: 'tests/screenshots/01-login-page.png',
      fullPage: true 
    });
    console.log('📸 Screenshot taken: 01-login-page.png');

    // Step 2: Fill login form
    console.log('📍 Step 2: Filling login form...');
    console.log(`Using test account: ${GLOBAL_TEST_ACCOUNT.email}`);
    
    // Wait for form elements to be visible
    await page.waitForSelector('input[name="email"]', { state: 'visible' });
    await page.waitForSelector('input[name="password"]', { state: 'visible' });
    
    // Fill the form
    await page.fill('input[name="email"]', GLOBAL_TEST_ACCOUNT.email);
    await page.fill('input[name="password"]', GLOBAL_TEST_ACCOUNT.password);
    
    // Take screenshot of filled form
    await page.screenshot({ 
      path: 'tests/screenshots/02-login-form-filled.png',
      fullPage: true 
    });
    console.log('📸 Screenshot taken: 02-login-form-filled.png');

    // Step 3: Submit login form
    console.log('📍 Step 3: Submitting login form...');
    await page.click('button[type="submit"]');
    
    // Wait for navigation to dashboard
    console.log('⏳ Waiting for redirect to dashboard...');
    await page.waitForURL('**/dashboard', { 
      timeout: TEST_CONFIG.NAVIGATION_TIMEOUT 
    });
    
    // Wait for dashboard to load completely
    await page.waitForLoadState('networkidle');
    
    console.log('✅ Successfully redirected to dashboard!');

    // Step 4: Verify dashboard page elements
    console.log('📍 Step 4: Verifying dashboard elements...');
    
    // Check for main dashboard title
    const dashboardTitle = await page.locator('h1').first();
    await expect(dashboardTitle).toContainText('Dashboard');
    console.log('✅ Dashboard title verified');

    // Check for financial summary cards
    const revenueCard = page.locator('text=Total Revenue').first();
    await expect(revenueCard).toBeVisible();
    console.log('✅ Revenue card visible');

    const expensesCard = page.locator('text=Total Expenses').first();
    await expect(expensesCard).toBeVisible();
    console.log('✅ Expenses card visible');

    const netIncomeCard = page.locator('text=Net Income').first();
    await expect(netIncomeCard).toBeVisible();
    console.log('✅ Net Income card visible');

    // Check for refresh button
    const refreshButton = page.locator('button:has-text("Refresh")');
    await expect(refreshButton).toBeVisible();
    console.log('✅ Refresh button visible');

    // Check for period selector
    const periodSelector = page.locator('text=Select Period').first();
    await expect(periodSelector).toBeVisible();
    console.log('✅ Period selector visible');

    // Take screenshot of dashboard
    await page.screenshot({ 
      path: 'tests/screenshots/03-dashboard-loaded.png',
      fullPage: true 
    });
    console.log('📸 Screenshot taken: 03-dashboard-loaded.png');

    // Step 5: Test dashboard interactions
    console.log('📍 Step 5: Testing dashboard interactions...');
    
    // Test period selector
    await periodSelector.click();
    await page.waitForSelector('text=Last 7 Days');
    await page.click('text=Last 7 Days');
    console.log('✅ Period selector interaction successful');

    // Test refresh button
    await refreshButton.click();
    await page.waitForTimeout(1000); // Wait for refresh to complete
    console.log('✅ Refresh button interaction successful');

    // Step 6: Test tab navigation
    console.log('📍 Step 6: Testing tab navigation...');
    
    // Check for tabs
    const customersTab = page.locator('text=Top Customers').first();
    const vendorsTab = page.locator('text=Top Vendors').first();
    const transactionsTab = page.locator('text=Recent Transactions').first();
    const agingTab = page.locator('text=Aging Summary').first();

    await expect(customersTab).toBeVisible();
    await expect(vendorsTab).toBeVisible();
    await expect(transactionsTab).toBeVisible();
    await expect(agingTab).toBeVisible();
    console.log('✅ All tabs visible');

    // Test tab switching
    await vendorsTab.click();
    await page.waitForTimeout(500);
    console.log('✅ Vendors tab clicked');

    await transactionsTab.click();
    await page.waitForTimeout(500);
    console.log('✅ Transactions tab clicked');

    await agingTab.click();
    await page.waitForTimeout(500);
    console.log('✅ Aging tab clicked');

    // Return to customers tab
    await customersTab.click();
    await page.waitForTimeout(500);
    console.log('✅ Returned to customers tab');

    // Take final screenshot
    await page.screenshot({ 
      path: 'tests/screenshots/04-dashboard-final.png',
      fullPage: true 
    });
    console.log('📸 Screenshot taken: 04-dashboard-final.png');

    // Step 7: Verify user authentication state
    console.log('📍 Step 7: Verifying authentication state...');
    
    // Check if user menu or profile is visible (this depends on your UI)
    // You might need to adjust this selector based on your actual UI
    const userMenu = page.locator('[data-testid="user-menu"]').or(
      page.locator('button:has-text("Profile")').or(
        page.locator('text=' + GLOBAL_TEST_ACCOUNT.name).first()
      )
    );
    
    // If user menu exists, verify it's accessible
    if (await userMenu.count() > 0) {
      await expect(userMenu.first()).toBeVisible();
      console.log('✅ User menu/profile visible');
    } else {
      console.log('ℹ️ User menu not found (may not be implemented yet)');
    }

    console.log('🎉 Complete dashboard login flow test completed successfully!');
  });

  test('Dashboard Data Loading and Error Handling', async ({ page }) => {
    console.log('🚀 Starting dashboard data loading test...');

    // Login first
    await page.goto(`${TEST_CONFIG.FRONTEND_URL.replace('3001', '3000')}/en/login`);
    await page.fill('input[name="email"]', GLOBAL_TEST_ACCOUNT.email);
    await page.fill('input[name="password"]', GLOBAL_TEST_ACCOUNT.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');

    // Test loading states and error handling
    console.log('📍 Testing loading states...');
    
    // Check for skeleton loaders or loading indicators
    const skeletonLoaders = page.locator('.skeleton, [data-testid="skeleton"], .loading');
    if (await skeletonLoaders.count() > 0) {
      console.log('✅ Loading indicators found');
    }

    // Wait for data to load
    await page.waitForLoadState('networkidle');
    
    // Verify no error states are showing
    const errorMessages = page.locator('text=Error, text=Failed, text=Something went wrong');
    const errorCount = await errorMessages.count();
    
    if (errorCount > 0) {
      console.log('⚠️ Error messages found on dashboard');
      await page.screenshot({ 
        path: 'tests/screenshots/05-dashboard-errors.png',
        fullPage: true 
      });
    } else {
      console.log('✅ No error messages found');
    }

    console.log('🎉 Dashboard data loading test completed!');
  });
});
