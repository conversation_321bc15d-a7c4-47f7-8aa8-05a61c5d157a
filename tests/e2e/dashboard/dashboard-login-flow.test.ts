/**
 * Dashboard Login Flow E2E Test
 *
 * This test follows the complete flow:
 * 1. Start both servers (Frontend and Backend)
 * 2. Use Puppeteer to login with test account
 * 3. Navigate to dashboard page
 * 4. Verify dashboard functionality and components
 * 5. Take screenshots for verification
 */

import { test, expect } from '@playwright/test';
import { GLOBAL_TEST_ACCOUNT, TEST_CONFIG } from '../../config/test-accounts';

test.describe('Dashboard Login Flow', () => {
  test.beforeAll(async () => {
    // Verify both servers are running
    console.log('🔍 Verifying servers are running...');

    // Check frontend server
    try {
      const frontendResponse = await fetch(`${TEST_CONFIG.FRONTEND_URL.replace('3001', '3000')}`);
      console.log(`✅ Frontend server status: ${frontendResponse.status}`);
    } catch (error) {
      console.error('❌ Frontend server not accessible:', error);
      throw new Error('Frontend server is not running on port 3000');
    }

    // Check backend server
    try {
      const backendResponse = await fetch(`${TEST_CONFIG.BACKEND_URL}/health`);
      console.log(`✅ Backend server status: ${backendResponse.status}`);
    } catch (error) {
      console.error('❌ Backend server not accessible:', error);
      throw new Error('Backend server is not running on port 8050');
    }

    console.log('🎉 Both servers are running successfully!');
  });

  test('Complete Login and Dashboard Flow', async ({ page }) => {
    console.log('🚀 Starting complete login and dashboard flow test...');

    // Step 1: Navigate to the application
    console.log('📍 Step 1: Navigating to login page...');
    await page.goto(`${TEST_CONFIG.FRONTEND_URL.replace('3001', '3000')}/en/login`);

    // Wait for page to load
    await page.waitForLoadState('networkidle');

    // Take screenshot of login page
    await page.screenshot({
      path: 'tests/screenshots/01-login-page.png',
      fullPage: true
    });
    console.log('📸 Screenshot taken: 01-login-page.png');

    // Step 2: Fill login form
    console.log('📍 Step 2: Filling login form...');
    console.log(`Using test account: ${GLOBAL_TEST_ACCOUNT.email}`);

    // Wait for form elements to be visible
    await page.waitForSelector('input[name="email"]', { state: 'visible' });
    await page.waitForSelector('input[name="password"]', { state: 'visible' });

    // Fill the form
    await page.fill('input[name="email"]', GLOBAL_TEST_ACCOUNT.email);
    await page.fill('input[name="password"]', GLOBAL_TEST_ACCOUNT.password);

    // Take screenshot of filled form
    await page.screenshot({
      path: 'tests/screenshots/02-login-form-filled.png',
      fullPage: true
    });
    console.log('📸 Screenshot taken: 02-login-form-filled.png');

    // Step 3: Submit login form
    console.log('📍 Step 3: Submitting login form...');
    await page.click('button[type="submit"]');

    // Wait for navigation to dashboard
    console.log('⏳ Waiting for redirect to dashboard...');
    await page.waitForURL('**/dashboard', {
      timeout: TEST_CONFIG.NAVIGATION_TIMEOUT
    });

    // Wait for dashboard to load completely
    await page.waitForLoadState('networkidle');

    console.log('✅ Successfully redirected to dashboard!');

    // Step 4: Verify dashboard page elements
    console.log('📍 Step 4: Verifying dashboard elements...');

    // Check for main dashboard title
    const dashboardTitle = await page.locator('h1').first();
    await expect(dashboardTitle).toContainText('Dashboard');
    console.log('✅ Dashboard title verified');

    // Check for dashboard content - be more flexible with text matching
    console.log('📍 Looking for dashboard content...');

    // Take screenshot first to see what's actually on the page
    await page.screenshot({
      path: 'tests/screenshots/03-dashboard-loaded.png',
      fullPage: true
    });
    console.log('📸 Screenshot taken: 03-dashboard-loaded.png');

    // Check for any financial cards or dashboard content
    const dashboardContent = page.locator('[data-testid="dashboard-content"], .dashboard-content, .financial-summary, .dashboard-cards');
    if (await dashboardContent.count() > 0) {
      await expect(dashboardContent.first()).toBeVisible();
      console.log('✅ Dashboard content area visible');
    } else {
      console.log('ℹ️ No specific dashboard content selectors found, checking for general content...');
    }

    // Look for any cards or summary elements
    const cards = page.locator('.card, [class*="card"], [data-testid*="card"]');
    if (await cards.count() > 0) {
      console.log(`✅ Found ${await cards.count()} card elements`);
    } else {
      console.log('ℹ️ No card elements found');
    }

    // Step 5: Test basic dashboard functionality
    console.log('📍 Step 5: Testing basic dashboard functionality...');

    // Look for any interactive elements
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    console.log(`ℹ️ Found ${buttonCount} buttons on the page`);

    // Look for any navigation elements
    const navElements = page.locator('nav, [role="navigation"], .navigation, .nav');
    if (await navElements.count() > 0) {
      console.log('✅ Navigation elements found');
    }

    // Check for any form elements
    const forms = page.locator('form, input, select, textarea');
    if (await forms.count() > 0) {
      console.log(`ℹ️ Found ${await forms.count()} form elements`);
    }

    // Take final screenshot
    await page.screenshot({
      path: 'tests/screenshots/04-dashboard-final.png',
      fullPage: true
    });
    console.log('📸 Screenshot taken: 04-dashboard-final.png');

    // Step 6: Verify user authentication state
    console.log('📍 Step 6: Verifying authentication state...');

    // Check if we're still on a dashboard URL
    const currentUrl = page.url();
    console.log(`Current URL: ${currentUrl}`);

    if (currentUrl.includes('/dashboard')) {
      console.log('✅ Still on dashboard page - authentication appears successful');
    }

    // Check for any user-related elements
    const userElements = page.locator('[data-testid*="user"], [class*="user"], button:has-text("Profile"), button:has-text("Logout")');
    if (await userElements.count() > 0) {
      console.log('✅ User-related elements found');
    } else {
      console.log('ℹ️ No specific user elements found');
    }

    console.log('🎉 Complete dashboard login flow test completed successfully!');
  });

  test('Dashboard Data Loading and Error Handling', async ({ page }) => {
    console.log('🚀 Starting dashboard data loading test...');

    // Login first
    await page.goto(`${TEST_CONFIG.FRONTEND_URL.replace('3001', '3000')}/en/login`);
    await page.fill('input[name="email"]', GLOBAL_TEST_ACCOUNT.email);
    await page.fill('input[name="password"]', GLOBAL_TEST_ACCOUNT.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');

    // Test loading states and error handling
    console.log('📍 Testing loading states...');

    // Check for skeleton loaders or loading indicators
    const skeletonLoaders = page.locator('.skeleton, [data-testid="skeleton"], .loading');
    if (await skeletonLoaders.count() > 0) {
      console.log('✅ Loading indicators found');
    }

    // Wait for data to load
    await page.waitForLoadState('networkidle');

    // Verify no error states are showing
    const errorMessages = page.locator('text=Error, text=Failed, text=Something went wrong');
    const errorCount = await errorMessages.count();

    if (errorCount > 0) {
      console.log('⚠️ Error messages found on dashboard');
      await page.screenshot({
        path: 'tests/screenshots/05-dashboard-errors.png',
        fullPage: true
      });
    } else {
      console.log('✅ No error messages found');
    }

    console.log('🎉 Dashboard data loading test completed!');
  });
});
