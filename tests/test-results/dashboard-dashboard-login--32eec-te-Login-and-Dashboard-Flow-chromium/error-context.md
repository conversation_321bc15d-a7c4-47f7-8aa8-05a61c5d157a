# Test info

- Name: Dashboard Login Flow >> Complete Login and Dashboard Flow
- Location: /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:41:7

# Error details

```
TimeoutError: page.waitForURL: Timeout 10000ms exceeded.
=========================== logs ===========================
waiting for navigation to "**/dashboard" until "load"
============================================================
    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:83:16
```

# Page snapshot

```yaml
- banner:
  - link "ADC Account":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "Reports":
      - /url: /reports
  - button "Toggle theme"
  - button "🇺🇸 English"
  - button "Login"
- complementary:
  - link "ADC Account":
    - /url: /en
  - heading "Overview" [level=3]
  - link "Dashboard":
    - /url: /en/dashboard
    - button "Dashboard"
  - heading "Financial Management" [level=3]
  - link "Chart of Accounts":
    - /url: /en/chart-of-accounts
    - button "Chart of Accounts"
  - link "Journal Entries":
    - /url: /en/journal-entries
    - button "Journal Entries"
  - link "Banking":
    - /url: /en/banking
    - button "Banking"
  - link "Assets":
    - /url: /en/assets
    - button "Assets"
  - heading "Sales & Purchases" [level=3]
  - link "Customers":
    - /url: /en/customers
    - button "Customers"
  - link "Invoices":
    - /url: /en/invoices
    - button "Invoices"
  - link "Vendors":
    - /url: /en/vendors
    - button "Vendors"
  - link "Bills":
    - /url: /en/bills
    - button "Bills"
  - link "Expenses":
    - /url: /en/expenses
    - button "Expenses"
  - heading "Other" [level=3]
  - link "Inventory":
    - /url: /en/inventory
    - button "Inventory"
  - link "Budget":
    - /url: /en/budget
    - button "Budget"
  - link "Cash Flow":
    - /url: /en/cashflow
    - button "Cash Flow"
  - link "Taxes":
    - /url: /en/taxes
    - button "Taxes"
  - link "Payroll":
    - /url: /en/payroll
    - button "Payroll"
  - heading "Reports" [level=3]
  - link "Reports":
    - /url: /en/reports
    - button "Reports"
  - link "Custom Reports":
    - /url: /en/reports/custom
    - button "Custom Reports"
  - link "A/R Aging":
    - /url: /en/reports/accounts-receivable-aging
    - button "A/R Aging"
  - link "Tax Reports":
    - /url: /en/taxes/reports
    - button "Tax Reports"
  - heading "System" [level=3]
  - link "Settings":
    - /url: /en/settings
    - button "Settings"
  - link "sidebar.links.organizations":
    - /url: /en/organizations
    - button "sidebar.links.organizations"
  - link "sidebar.links.branches":
    - /url: /en/branches
    - button "sidebar.links.branches"
  - link "Merchants":
    - /url: /en/settings/merchants
    - button "Merchants"
  - link "Integrations":
    - /url: /en/integrations
    - button "Integrations"
  - link "User Management":
    - /url: /en/users
    - button "User Management"
  - link "Pricing & Plans":
    - /url: /en/pricing
    - button "Pricing & Plans"
- main:
  - text: Sign In Use Google OAuth for quick access, or enter your existing account credentials
  - alert: Login Failed Authentication rate limit exceeded
  - text: Email
  - textbox "Email": <EMAIL>
  - text: Password
  - link "Forgot password?":
    - /url: /en/forgot-password
  - textbox "Password": testpassword123
  - button "Sign In"
  - text: Or continue with
  - button "Continue with Google (Recommended)":
    - img
    - text: Continue with Google (Recommended)
  - text: "💡 New users: Use Google login above. Email/password requires an existing account. Don't have an account?"
  - link "Sign up":
    - /url: /en/register
- contentinfo:
  - paragraph: © 2025 ADC Account. All rights reserved.
  - navigation:
    - link "Terms of Service":
      - /url: /en/terms
    - link "Privacy Policy":
      - /url: /en/privacy
    - link "Help":
      - /url: /en/help
- region "Notifications alt+T"
- region "Notifications (F8)":
  - list
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 2 Issue
- button "Collapse issues badge":
  - img
- alert
```

# Test source

```ts
   1 | /**
   2 |  * Dashboard Login Flow E2E Test
   3 |  *
   4 |  * This test follows the complete flow:
   5 |  * 1. Start both servers (Frontend and Backend)
   6 |  * 2. Use Puppeteer to login with test account
   7 |  * 3. Navigate to dashboard page
   8 |  * 4. Verify dashboard functionality and components
   9 |  * 5. Take screenshots for verification
   10 |  */
   11 |
   12 | import { test, expect } from '@playwright/test';
   13 | import { GLOBAL_TEST_ACCOUNT, TEST_CONFIG } from '../../config/test-accounts';
   14 |
   15 | test.describe('Dashboard Login Flow', () => {
   16 |   test.beforeAll(async () => {
   17 |     // Verify both servers are running
   18 |     console.log('🔍 Verifying servers are running...');
   19 |
   20 |     // Check frontend server
   21 |     try {
   22 |       const frontendResponse = await fetch(`${TEST_CONFIG.FRONTEND_URL.replace('3001', '3000')}`);
   23 |       console.log(`✅ Frontend server status: ${frontendResponse.status}`);
   24 |     } catch (error) {
   25 |       console.error('❌ Frontend server not accessible:', error);
   26 |       throw new Error('Frontend server is not running on port 3000');
   27 |     }
   28 |
   29 |     // Check backend server
   30 |     try {
   31 |       const backendResponse = await fetch(`${TEST_CONFIG.BACKEND_URL}/health`);
   32 |       console.log(`✅ Backend server status: ${backendResponse.status}`);
   33 |     } catch (error) {
   34 |       console.error('❌ Backend server not accessible:', error);
   35 |       throw new Error('Backend server is not running on port 8050');
   36 |     }
   37 |
   38 |     console.log('🎉 Both servers are running successfully!');
   39 |   });
   40 |
   41 |   test('Complete Login and Dashboard Flow', async ({ page }) => {
   42 |     console.log('🚀 Starting complete login and dashboard flow test...');
   43 |
   44 |     // Step 1: Navigate to the application
   45 |     console.log('📍 Step 1: Navigating to login page...');
   46 |     await page.goto(`${TEST_CONFIG.FRONTEND_URL.replace('3001', '3000')}/en/login`);
   47 |
   48 |     // Wait for page to load
   49 |     await page.waitForLoadState('networkidle');
   50 |
   51 |     // Take screenshot of login page
   52 |     await page.screenshot({
   53 |       path: 'tests/screenshots/01-login-page.png',
   54 |       fullPage: true
   55 |     });
   56 |     console.log('📸 Screenshot taken: 01-login-page.png');
   57 |
   58 |     // Step 2: Fill login form
   59 |     console.log('📍 Step 2: Filling login form...');
   60 |     console.log(`Using test account: ${GLOBAL_TEST_ACCOUNT.email}`);
   61 |
   62 |     // Wait for form elements to be visible
   63 |     await page.waitForSelector('input[name="email"]', { state: 'visible' });
   64 |     await page.waitForSelector('input[name="password"]', { state: 'visible' });
   65 |
   66 |     // Fill the form
   67 |     await page.fill('input[name="email"]', GLOBAL_TEST_ACCOUNT.email);
   68 |     await page.fill('input[name="password"]', GLOBAL_TEST_ACCOUNT.password);
   69 |
   70 |     // Take screenshot of filled form
   71 |     await page.screenshot({
   72 |       path: 'tests/screenshots/02-login-form-filled.png',
   73 |       fullPage: true
   74 |     });
   75 |     console.log('📸 Screenshot taken: 02-login-form-filled.png');
   76 |
   77 |     // Step 3: Submit login form
   78 |     console.log('📍 Step 3: Submitting login form...');
   79 |     await page.click('button[type="submit"]');
   80 |
   81 |     // Wait for navigation to dashboard
   82 |     console.log('⏳ Waiting for redirect to dashboard...');
>  83 |     await page.waitForURL('**/dashboard', {
      |                ^ TimeoutError: page.waitForURL: Timeout 10000ms exceeded.
   84 |       timeout: TEST_CONFIG.NAVIGATION_TIMEOUT
   85 |     });
   86 |
   87 |     // Wait for dashboard to load completely
   88 |     await page.waitForLoadState('networkidle');
   89 |
   90 |     console.log('✅ Successfully redirected to dashboard!');
   91 |
   92 |     // Step 4: Verify dashboard page elements
   93 |     console.log('📍 Step 4: Verifying dashboard elements...');
   94 |
   95 |     // Check for main dashboard title
   96 |     const dashboardTitle = await page.locator('h1').first();
   97 |     await expect(dashboardTitle).toContainText('Dashboard');
   98 |     console.log('✅ Dashboard title verified');
   99 |
  100 |     // Check for dashboard content - be more flexible with text matching
  101 |     console.log('📍 Looking for dashboard content...');
  102 |
  103 |     // Take screenshot first to see what's actually on the page
  104 |     await page.screenshot({
  105 |       path: 'tests/screenshots/03-dashboard-loaded.png',
  106 |       fullPage: true
  107 |     });
  108 |     console.log('📸 Screenshot taken: 03-dashboard-loaded.png');
  109 |
  110 |     // Check for any financial cards or dashboard content
  111 |     const dashboardContent = page.locator('[data-testid="dashboard-content"], .dashboard-content, .financial-summary, .dashboard-cards');
  112 |     if (await dashboardContent.count() > 0) {
  113 |       await expect(dashboardContent.first()).toBeVisible();
  114 |       console.log('✅ Dashboard content area visible');
  115 |     } else {
  116 |       console.log('ℹ️ No specific dashboard content selectors found, checking for general content...');
  117 |     }
  118 |
  119 |     // Look for any cards or summary elements
  120 |     const cards = page.locator('.card, [class*="card"], [data-testid*="card"]');
  121 |     if (await cards.count() > 0) {
  122 |       console.log(`✅ Found ${await cards.count()} card elements`);
  123 |     } else {
  124 |       console.log('ℹ️ No card elements found');
  125 |     }
  126 |
  127 |     // Step 5: Test basic dashboard functionality
  128 |     console.log('📍 Step 5: Testing basic dashboard functionality...');
  129 |
  130 |     // Look for any interactive elements
  131 |     const buttons = page.locator('button');
  132 |     const buttonCount = await buttons.count();
  133 |     console.log(`ℹ️ Found ${buttonCount} buttons on the page`);
  134 |
  135 |     // Look for any navigation elements
  136 |     const navElements = page.locator('nav, [role="navigation"], .navigation, .nav');
  137 |     if (await navElements.count() > 0) {
  138 |       console.log('✅ Navigation elements found');
  139 |     }
  140 |
  141 |     // Check for any form elements
  142 |     const forms = page.locator('form, input, select, textarea');
  143 |     if (await forms.count() > 0) {
  144 |       console.log(`ℹ️ Found ${await forms.count()} form elements`);
  145 |     }
  146 |
  147 |     // Take final screenshot
  148 |     await page.screenshot({
  149 |       path: 'tests/screenshots/04-dashboard-final.png',
  150 |       fullPage: true
  151 |     });
  152 |     console.log('📸 Screenshot taken: 04-dashboard-final.png');
  153 |
  154 |     // Step 6: Verify user authentication state
  155 |     console.log('📍 Step 6: Verifying authentication state...');
  156 |
  157 |     // Check if we're still on a dashboard URL
  158 |     const currentUrl = page.url();
  159 |     console.log(`Current URL: ${currentUrl}`);
  160 |
  161 |     if (currentUrl.includes('/dashboard')) {
  162 |       console.log('✅ Still on dashboard page - authentication appears successful');
  163 |     }
  164 |
  165 |     // Check for any user-related elements
  166 |     const userElements = page.locator('[data-testid*="user"], [class*="user"], button:has-text("Profile"), button:has-text("Logout")');
  167 |     if (await userElements.count() > 0) {
  168 |       console.log('✅ User-related elements found');
  169 |     } else {
  170 |       console.log('ℹ️ No specific user elements found');
  171 |     }
  172 |
  173 |     console.log('🎉 Complete dashboard login flow test completed successfully!');
  174 |   });
  175 |
  176 |   test('Dashboard Data Loading and Error Handling', async ({ page }) => {
  177 |     console.log('🚀 Starting dashboard data loading test...');
  178 |
  179 |     // Login first
  180 |     await page.goto(`${TEST_CONFIG.FRONTEND_URL.replace('3001', '3000')}/en/login`);
  181 |     await page.fill('input[name="email"]', GLOBAL_TEST_ACCOUNT.email);
  182 |     await page.fill('input[name="password"]', GLOBAL_TEST_ACCOUNT.password);
  183 |     await page.click('button[type="submit"]');
```