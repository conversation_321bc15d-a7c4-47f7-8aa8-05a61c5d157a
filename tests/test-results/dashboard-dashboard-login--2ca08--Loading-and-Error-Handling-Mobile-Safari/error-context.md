# Test info

- Name: Dashboard Login Flow >> Dashboard Data Loading and Error Handling
- Location: /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:206:7

# Error details

```
TimeoutError: page.waitForURL: Timeout 10000ms exceeded.
=========================== logs ===========================
waiting for navigation to "**/dashboard" until "load"
============================================================
    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:214:16
```

# Page snapshot

```yaml
- banner:
  - button "Toggle sidebar"
  - link "ADC Account":
    - /url: /
  - button "Toggle theme"
  - button "🇺🇸"
  - button "Login"
- complementary:
  - link "ADC Account":
    - /url: /en
  - heading "Overview" [level=3]
  - link "Dashboard":
    - /url: /en/dashboard
    - button "Dashboard"
  - heading "Financial Management" [level=3]
  - link "Chart of Accounts":
    - /url: /en/chart-of-accounts
    - button "Chart of Accounts"
  - link "Journal Entries":
    - /url: /en/journal-entries
    - button "Journal Entries"
  - link "Banking":
    - /url: /en/banking
    - button "Banking"
  - link "Assets":
    - /url: /en/assets
    - button "Assets"
  - heading "Sales & Purchases" [level=3]
  - link "Customers":
    - /url: /en/customers
    - button "Customers"
  - link "Invoices":
    - /url: /en/invoices
    - button "Invoices"
  - link "Vendors":
    - /url: /en/vendors
    - button "Vendors"
  - link "Bills":
    - /url: /en/bills
    - button "Bills"
  - link "Expenses":
    - /url: /en/expenses
    - button "Expenses"
  - heading "Other" [level=3]
  - link "Inventory":
    - /url: /en/inventory
    - button "Inventory"
  - link "Budget":
    - /url: /en/budget
    - button "Budget"
  - link "Cash Flow":
    - /url: /en/cashflow
    - button "Cash Flow"
  - link "Taxes":
    - /url: /en/taxes
    - button "Taxes"
  - link "Payroll":
    - /url: /en/payroll
    - button "Payroll"
  - heading "Reports" [level=3]
  - link "Reports":
    - /url: /en/reports
    - button "Reports"
  - link "Custom Reports":
    - /url: /en/reports/custom
    - button "Custom Reports"
  - link "A/R Aging":
    - /url: /en/reports/accounts-receivable-aging
    - button "A/R Aging"
  - link "Tax Reports":
    - /url: /en/taxes/reports
    - button "Tax Reports"
  - heading "System" [level=3]
  - link "Settings":
    - /url: /en/settings
    - button "Settings"
  - link "sidebar.links.organizations":
    - /url: /en/organizations
    - button "sidebar.links.organizations"
  - link "sidebar.links.branches":
    - /url: /en/branches
    - button "sidebar.links.branches"
  - link "Merchants":
    - /url: /en/settings/merchants
    - button "Merchants"
  - link "Integrations":
    - /url: /en/integrations
    - button "Integrations"
  - link "User Management":
    - /url: /en/users
    - button "User Management"
  - link "Pricing & Plans":
    - /url: /en/pricing
    - button "Pricing & Plans"
- main:
  - text: Sign In Use Google OAuth for quick access, or enter your existing account credentials
  - alert: Login Failed Authentication rate limit exceeded
  - text: Email
  - textbox "Email": <EMAIL>
  - text: Password
  - link "Forgot password?":
    - /url: /en/forgot-password
  - textbox "Password": testpassword123
  - button "Sign In"
  - text: Or continue with
  - button "Continue with Google (Recommended)":
    - img
    - text: Continue with Google (Recommended)
  - text: "💡 New users: Use Google login above. Email/password requires an existing account. Don't have an account?"
  - link "Sign up":
    - /url: /en/register
- contentinfo:
  - paragraph: © 2025 ADC Account. All rights reserved.
  - navigation:
    - link "Terms of Service":
      - /url: /en/terms
    - link "Privacy Policy":
      - /url: /en/privacy
    - link "Help":
      - /url: /en/help
- region "Notifications alt+T"
- region "Notifications (F8)":
  - list
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 1 Issue
- button "Collapse issues badge":
  - img
- alert
```

# Test source

```ts
  114 |     const refreshButton = page.locator('button:has-text("Refresh")');
  115 |     await expect(refreshButton).toBeVisible();
  116 |     console.log('✅ Refresh button visible');
  117 |
  118 |     // Check for period selector
  119 |     const periodSelector = page.locator('text=Select Period').first();
  120 |     await expect(periodSelector).toBeVisible();
  121 |     console.log('✅ Period selector visible');
  122 |
  123 |     // Take screenshot of dashboard
  124 |     await page.screenshot({ 
  125 |       path: 'tests/screenshots/03-dashboard-loaded.png',
  126 |       fullPage: true 
  127 |     });
  128 |     console.log('📸 Screenshot taken: 03-dashboard-loaded.png');
  129 |
  130 |     // Step 5: Test dashboard interactions
  131 |     console.log('📍 Step 5: Testing dashboard interactions...');
  132 |     
  133 |     // Test period selector
  134 |     await periodSelector.click();
  135 |     await page.waitForSelector('text=Last 7 Days');
  136 |     await page.click('text=Last 7 Days');
  137 |     console.log('✅ Period selector interaction successful');
  138 |
  139 |     // Test refresh button
  140 |     await refreshButton.click();
  141 |     await page.waitForTimeout(1000); // Wait for refresh to complete
  142 |     console.log('✅ Refresh button interaction successful');
  143 |
  144 |     // Step 6: Test tab navigation
  145 |     console.log('📍 Step 6: Testing tab navigation...');
  146 |     
  147 |     // Check for tabs
  148 |     const customersTab = page.locator('text=Top Customers').first();
  149 |     const vendorsTab = page.locator('text=Top Vendors').first();
  150 |     const transactionsTab = page.locator('text=Recent Transactions').first();
  151 |     const agingTab = page.locator('text=Aging Summary').first();
  152 |
  153 |     await expect(customersTab).toBeVisible();
  154 |     await expect(vendorsTab).toBeVisible();
  155 |     await expect(transactionsTab).toBeVisible();
  156 |     await expect(agingTab).toBeVisible();
  157 |     console.log('✅ All tabs visible');
  158 |
  159 |     // Test tab switching
  160 |     await vendorsTab.click();
  161 |     await page.waitForTimeout(500);
  162 |     console.log('✅ Vendors tab clicked');
  163 |
  164 |     await transactionsTab.click();
  165 |     await page.waitForTimeout(500);
  166 |     console.log('✅ Transactions tab clicked');
  167 |
  168 |     await agingTab.click();
  169 |     await page.waitForTimeout(500);
  170 |     console.log('✅ Aging tab clicked');
  171 |
  172 |     // Return to customers tab
  173 |     await customersTab.click();
  174 |     await page.waitForTimeout(500);
  175 |     console.log('✅ Returned to customers tab');
  176 |
  177 |     // Take final screenshot
  178 |     await page.screenshot({ 
  179 |       path: 'tests/screenshots/04-dashboard-final.png',
  180 |       fullPage: true 
  181 |     });
  182 |     console.log('📸 Screenshot taken: 04-dashboard-final.png');
  183 |
  184 |     // Step 7: Verify user authentication state
  185 |     console.log('📍 Step 7: Verifying authentication state...');
  186 |     
  187 |     // Check if user menu or profile is visible (this depends on your UI)
  188 |     // You might need to adjust this selector based on your actual UI
  189 |     const userMenu = page.locator('[data-testid="user-menu"]').or(
  190 |       page.locator('button:has-text("Profile")').or(
  191 |         page.locator('text=' + GLOBAL_TEST_ACCOUNT.name).first()
  192 |       )
  193 |     );
  194 |     
  195 |     // If user menu exists, verify it's accessible
  196 |     if (await userMenu.count() > 0) {
  197 |       await expect(userMenu.first()).toBeVisible();
  198 |       console.log('✅ User menu/profile visible');
  199 |     } else {
  200 |       console.log('ℹ️ User menu not found (may not be implemented yet)');
  201 |     }
  202 |
  203 |     console.log('🎉 Complete dashboard login flow test completed successfully!');
  204 |   });
  205 |
  206 |   test('Dashboard Data Loading and Error Handling', async ({ page }) => {
  207 |     console.log('🚀 Starting dashboard data loading test...');
  208 |
  209 |     // Login first
  210 |     await page.goto(`${TEST_CONFIG.FRONTEND_URL.replace('3001', '3000')}/en/login`);
  211 |     await page.fill('input[name="email"]', GLOBAL_TEST_ACCOUNT.email);
  212 |     await page.fill('input[name="password"]', GLOBAL_TEST_ACCOUNT.password);
  213 |     await page.click('button[type="submit"]');
> 214 |     await page.waitForURL('**/dashboard');
      |                ^ TimeoutError: page.waitForURL: Timeout 10000ms exceeded.
  215 |
  216 |     // Test loading states and error handling
  217 |     console.log('📍 Testing loading states...');
  218 |     
  219 |     // Check for skeleton loaders or loading indicators
  220 |     const skeletonLoaders = page.locator('.skeleton, [data-testid="skeleton"], .loading');
  221 |     if (await skeletonLoaders.count() > 0) {
  222 |       console.log('✅ Loading indicators found');
  223 |     }
  224 |
  225 |     // Wait for data to load
  226 |     await page.waitForLoadState('networkidle');
  227 |     
  228 |     // Verify no error states are showing
  229 |     const errorMessages = page.locator('text=Error, text=Failed, text=Something went wrong');
  230 |     const errorCount = await errorMessages.count();
  231 |     
  232 |     if (errorCount > 0) {
  233 |       console.log('⚠️ Error messages found on dashboard');
  234 |       await page.screenshot({ 
  235 |         path: 'tests/screenshots/05-dashboard-errors.png',
  236 |         fullPage: true 
  237 |       });
  238 |     } else {
  239 |       console.log('✅ No error messages found');
  240 |     }
  241 |
  242 |     console.log('🎉 Dashboard data loading test completed!');
  243 |   });
  244 | });
  245 |
```