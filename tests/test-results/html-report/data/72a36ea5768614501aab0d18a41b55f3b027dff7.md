# Test info

- Name: Dashboard Login Flow >> Complete Login and Dashboard Flow
- Location: /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:41:7

# Error details

```
TimeoutError: page.waitForURL: Timeout 10000ms exceeded.
=========================== logs ===========================
waiting for navigation to "**/dashboard" until "load"
============================================================
    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:83:16
```

# Page snapshot

```yaml
- banner:
  - button "Toggle sidebar"
  - link "ADC Account":
    - /url: /
  - button "Toggle theme"
  - button "🇺🇸"
  - button "Login"
- complementary:
  - link "ADC Account":
    - /url: /en
  - heading "Overview" [level=3]
  - link "Dashboard":
    - /url: /en/dashboard
    - button "Dashboard"
  - heading "Financial Management" [level=3]
  - link "Chart of Accounts":
    - /url: /en/chart-of-accounts
    - button "Chart of Accounts"
  - link "Journal Entries":
    - /url: /en/journal-entries
    - button "Journal Entries"
  - link "Banking":
    - /url: /en/banking
    - button "Banking"
  - link "Assets":
    - /url: /en/assets
    - button "Assets"
  - heading "Sales & Purchases" [level=3]
  - link "Customers":
    - /url: /en/customers
    - button "Customers"
  - link "Invoices":
    - /url: /en/invoices
    - button "Invoices"
  - link "Vendors":
    - /url: /en/vendors
    - button "Vendors"
  - link "Bills":
    - /url: /en/bills
    - button "Bills"
  - link "Expenses":
    - /url: /en/expenses
    - button "Expenses"
  - heading "Other" [level=3]
  - link "Inventory":
    - /url: /en/inventory
    - button "Inventory"
  - link "Budget":
    - /url: /en/budget
    - button "Budget"
  - link "Cash Flow":
    - /url: /en/cashflow
    - button "Cash Flow"
  - link "Taxes":
    - /url: /en/taxes
    - button "Taxes"
  - link "Payroll":
    - /url: /en/payroll
    - button "Payroll"
  - heading "Reports" [level=3]
  - link "Reports":
    - /url: /en/reports
    - button "Reports"
  - link "Custom Reports":
    - /url: /en/reports/custom
    - button "Custom Reports"
  - link "A/R Aging":
    - /url: /en/reports/accounts-receivable-aging
    - button "A/R Aging"
  - link "Tax Reports":
    - /url: /en/taxes/reports
    - button "Tax Reports"
  - heading "System" [level=3]
  - link "Settings":
    - /url: /en/settings
    - button "Settings"
  - link "sidebar.links.organizations":
    - /url: /en/organizations
    - button "sidebar.links.organizations"
  - link "sidebar.links.branches":
    - /url: /en/branches
    - button "sidebar.links.branches"
  - link "Merchants":
    - /url: /en/settings/merchants
    - button "Merchants"
  - link "Integrations":
    - /url: /en/integrations
    - button "Integrations"
  - link "User Management":
    - /url: /en/users
    - button "User Management"
  - link "Pricing & Plans":
    - /url: /en/pricing
    - button "Pricing & Plans"
- main:
  - text: Sign In Use Google OAuth for quick access, or enter your existing account credentials
  - alert: Login Failed Authentication rate limit exceeded
  - text: Email
  - textbox "Email": <EMAIL>
  - text: Password
  - link "Forgot password?":
    - /url: /en/forgot-password
  - textbox "Password": testpassword123
  - button "Sign In"
  - text: Or continue with
  - button "Continue with Google (Recommended)":
    - img
    - text: Continue with Google (Recommended)
  - text: "💡 New users: Use Google login above. Email/password requires an existing account. Don't have an account?"
  - link "Sign up":
    - /url: /en/register
- contentinfo:
  - paragraph: © 2025 ADC Account. All rights reserved.
  - navigation:
    - link "Terms of Service":
      - /url: /en/terms
    - link "Privacy Policy":
      - /url: /en/privacy
    - link "Help":
      - /url: /en/help
- region "Notifications alt+T"
- region "Notifications (F8)":
  - list
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 2 Issue
- button "Collapse issues badge":
  - img
- alert
```

# Test source

```ts
   1 | /**
   2 |  * Dashboard Login Flow E2E Test
   3 |  * 
   4 |  * This test follows the complete flow:
   5 |  * 1. Start both servers (Frontend and Backend)
   6 |  * 2. Use Puppeteer to login with test account
   7 |  * 3. Navigate to dashboard page
   8 |  * 4. Verify dashboard functionality and components
   9 |  * 5. Take screenshots for verification
   10 |  */
   11 |
   12 | import { test, expect } from '@playwright/test';
   13 | import { GLOBAL_TEST_ACCOUNT, TEST_CONFIG } from '../../config/test-accounts';
   14 |
   15 | test.describe('Dashboard Login Flow', () => {
   16 |   test.beforeAll(async () => {
   17 |     // Verify both servers are running
   18 |     console.log('🔍 Verifying servers are running...');
   19 |     
   20 |     // Check frontend server
   21 |     try {
   22 |       const frontendResponse = await fetch(`${TEST_CONFIG.FRONTEND_URL.replace('3001', '3000')}`);
   23 |       console.log(`✅ Frontend server status: ${frontendResponse.status}`);
   24 |     } catch (error) {
   25 |       console.error('❌ Frontend server not accessible:', error);
   26 |       throw new Error('Frontend server is not running on port 3000');
   27 |     }
   28 |
   29 |     // Check backend server
   30 |     try {
   31 |       const backendResponse = await fetch(`${TEST_CONFIG.BACKEND_URL}/health`);
   32 |       console.log(`✅ Backend server status: ${backendResponse.status}`);
   33 |     } catch (error) {
   34 |       console.error('❌ Backend server not accessible:', error);
   35 |       throw new Error('Backend server is not running on port 8050');
   36 |     }
   37 |
   38 |     console.log('🎉 Both servers are running successfully!');
   39 |   });
   40 |
   41 |   test('Complete Login and Dashboard Flow', async ({ page }) => {
   42 |     console.log('🚀 Starting complete login and dashboard flow test...');
   43 |
   44 |     // Step 1: Navigate to the application
   45 |     console.log('📍 Step 1: Navigating to login page...');
   46 |     await page.goto(`${TEST_CONFIG.FRONTEND_URL.replace('3001', '3000')}/en/login`);
   47 |     
   48 |     // Wait for page to load
   49 |     await page.waitForLoadState('networkidle');
   50 |     
   51 |     // Take screenshot of login page
   52 |     await page.screenshot({ 
   53 |       path: 'tests/screenshots/01-login-page.png',
   54 |       fullPage: true 
   55 |     });
   56 |     console.log('📸 Screenshot taken: 01-login-page.png');
   57 |
   58 |     // Step 2: Fill login form
   59 |     console.log('📍 Step 2: Filling login form...');
   60 |     console.log(`Using test account: ${GLOBAL_TEST_ACCOUNT.email}`);
   61 |     
   62 |     // Wait for form elements to be visible
   63 |     await page.waitForSelector('input[name="email"]', { state: 'visible' });
   64 |     await page.waitForSelector('input[name="password"]', { state: 'visible' });
   65 |     
   66 |     // Fill the form
   67 |     await page.fill('input[name="email"]', GLOBAL_TEST_ACCOUNT.email);
   68 |     await page.fill('input[name="password"]', GLOBAL_TEST_ACCOUNT.password);
   69 |     
   70 |     // Take screenshot of filled form
   71 |     await page.screenshot({ 
   72 |       path: 'tests/screenshots/02-login-form-filled.png',
   73 |       fullPage: true 
   74 |     });
   75 |     console.log('📸 Screenshot taken: 02-login-form-filled.png');
   76 |
   77 |     // Step 3: Submit login form
   78 |     console.log('📍 Step 3: Submitting login form...');
   79 |     await page.click('button[type="submit"]');
   80 |     
   81 |     // Wait for navigation to dashboard
   82 |     console.log('⏳ Waiting for redirect to dashboard...');
>  83 |     await page.waitForURL('**/dashboard', { 
      |                ^ TimeoutError: page.waitForURL: Timeout 10000ms exceeded.
   84 |       timeout: TEST_CONFIG.NAVIGATION_TIMEOUT 
   85 |     });
   86 |     
   87 |     // Wait for dashboard to load completely
   88 |     await page.waitForLoadState('networkidle');
   89 |     
   90 |     console.log('✅ Successfully redirected to dashboard!');
   91 |
   92 |     // Step 4: Verify dashboard page elements
   93 |     console.log('📍 Step 4: Verifying dashboard elements...');
   94 |     
   95 |     // Check for main dashboard title
   96 |     const dashboardTitle = await page.locator('h1').first();
   97 |     await expect(dashboardTitle).toContainText('Dashboard');
   98 |     console.log('✅ Dashboard title verified');
   99 |
  100 |     // Check for financial summary cards
  101 |     const revenueCard = page.locator('text=Total Revenue').first();
  102 |     await expect(revenueCard).toBeVisible();
  103 |     console.log('✅ Revenue card visible');
  104 |
  105 |     const expensesCard = page.locator('text=Total Expenses').first();
  106 |     await expect(expensesCard).toBeVisible();
  107 |     console.log('✅ Expenses card visible');
  108 |
  109 |     const netIncomeCard = page.locator('text=Net Income').first();
  110 |     await expect(netIncomeCard).toBeVisible();
  111 |     console.log('✅ Net Income card visible');
  112 |
  113 |     // Check for refresh button
  114 |     const refreshButton = page.locator('button:has-text("Refresh")');
  115 |     await expect(refreshButton).toBeVisible();
  116 |     console.log('✅ Refresh button visible');
  117 |
  118 |     // Check for period selector
  119 |     const periodSelector = page.locator('text=Select Period').first();
  120 |     await expect(periodSelector).toBeVisible();
  121 |     console.log('✅ Period selector visible');
  122 |
  123 |     // Take screenshot of dashboard
  124 |     await page.screenshot({ 
  125 |       path: 'tests/screenshots/03-dashboard-loaded.png',
  126 |       fullPage: true 
  127 |     });
  128 |     console.log('📸 Screenshot taken: 03-dashboard-loaded.png');
  129 |
  130 |     // Step 5: Test dashboard interactions
  131 |     console.log('📍 Step 5: Testing dashboard interactions...');
  132 |     
  133 |     // Test period selector
  134 |     await periodSelector.click();
  135 |     await page.waitForSelector('text=Last 7 Days');
  136 |     await page.click('text=Last 7 Days');
  137 |     console.log('✅ Period selector interaction successful');
  138 |
  139 |     // Test refresh button
  140 |     await refreshButton.click();
  141 |     await page.waitForTimeout(1000); // Wait for refresh to complete
  142 |     console.log('✅ Refresh button interaction successful');
  143 |
  144 |     // Step 6: Test tab navigation
  145 |     console.log('📍 Step 6: Testing tab navigation...');
  146 |     
  147 |     // Check for tabs
  148 |     const customersTab = page.locator('text=Top Customers').first();
  149 |     const vendorsTab = page.locator('text=Top Vendors').first();
  150 |     const transactionsTab = page.locator('text=Recent Transactions').first();
  151 |     const agingTab = page.locator('text=Aging Summary').first();
  152 |
  153 |     await expect(customersTab).toBeVisible();
  154 |     await expect(vendorsTab).toBeVisible();
  155 |     await expect(transactionsTab).toBeVisible();
  156 |     await expect(agingTab).toBeVisible();
  157 |     console.log('✅ All tabs visible');
  158 |
  159 |     // Test tab switching
  160 |     await vendorsTab.click();
  161 |     await page.waitForTimeout(500);
  162 |     console.log('✅ Vendors tab clicked');
  163 |
  164 |     await transactionsTab.click();
  165 |     await page.waitForTimeout(500);
  166 |     console.log('✅ Transactions tab clicked');
  167 |
  168 |     await agingTab.click();
  169 |     await page.waitForTimeout(500);
  170 |     console.log('✅ Aging tab clicked');
  171 |
  172 |     // Return to customers tab
  173 |     await customersTab.click();
  174 |     await page.waitForTimeout(500);
  175 |     console.log('✅ Returned to customers tab');
  176 |
  177 |     // Take final screenshot
  178 |     await page.screenshot({ 
  179 |       path: 'tests/screenshots/04-dashboard-final.png',
  180 |       fullPage: true 
  181 |     });
  182 |     console.log('📸 Screenshot taken: 04-dashboard-final.png');
  183 |
```