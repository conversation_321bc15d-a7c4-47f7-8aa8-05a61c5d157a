{"config": {"configFile": "/Users/<USER>/Desktop/adc/adc-account-web/tests/playwright.config.ts", "rootDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Desktop/adc/adc-account-web/tests/config/global-setup.ts", "globalTeardown": "/Users/<USER>/Desktop/adc/adc-account-web/tests/config/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 4, "webServer": null}, "suites": [{"title": "auth/login.test.ts", "file": "auth/login.test.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Login Flow", "file": "auth/login.test.ts", "line": 10, "column": 6, "specs": [{"title": "should display login form correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-26b407998d546a044a95", "file": "auth/login.test.ts", "line": 21, "column": 7}, {"title": "should successfully login with global test account", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-20fbccc0d5432eaf0cf3", "file": "auth/login.test.ts", "line": 37, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-0ede0f28976c52c47826", "file": "auth/login.test.ts", "line": 55, "column": 7}, {"title": "should show error for non-existent user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-9ef687d48d5b0d2863ef", "file": "auth/login.test.ts", "line": 72, "column": 7}, {"title": "should handle form validation correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-ae77ea7e831276a806be", "file": "auth/login.test.ts", "line": 89, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-ae19c434125cd0ea4430", "file": "auth/login.test.ts", "line": 110, "column": 7}, {"title": "should maintain form state during validation errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-5da03b5f871ab53be747", "file": "auth/login.test.ts", "line": 121, "column": 7}, {"title": "should have working navigation links", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-0ae1d5fe227c49937f95", "file": "auth/login.test.ts", "line": 138, "column": 7}, {"title": "should handle loading states correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-7cbf4d290512380842d7", "file": "auth/login.test.ts", "line": 151, "column": 7}, {"title": "should support keyboard navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-6de421e2efc4981e58ac", "file": "auth/login.test.ts", "line": 175, "column": 7}, {"title": "should display login form correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-ada2a5b909fe97d64b83", "file": "auth/login.test.ts", "line": 21, "column": 7}, {"title": "should successfully login with global test account", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-e3c125d762f3bfd6d91b", "file": "auth/login.test.ts", "line": 37, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-51c2d1b5f1be6075e135", "file": "auth/login.test.ts", "line": 55, "column": 7}, {"title": "should show error for non-existent user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-b8ed985f34cca96b3370", "file": "auth/login.test.ts", "line": 72, "column": 7}, {"title": "should handle form validation correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-643b0b88fb405d11ce09", "file": "auth/login.test.ts", "line": 89, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-10d7c84a8b590edc92fc", "file": "auth/login.test.ts", "line": 110, "column": 7}, {"title": "should maintain form state during validation errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-c3ae37e0bf61881a7b8c", "file": "auth/login.test.ts", "line": 121, "column": 7}, {"title": "should have working navigation links", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-520111710a2aa1d68c72", "file": "auth/login.test.ts", "line": 138, "column": 7}, {"title": "should handle loading states correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-10c26271565ea1d5533f", "file": "auth/login.test.ts", "line": 151, "column": 7}, {"title": "should support keyboard navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-922ab7ad6d7429f871c7", "file": "auth/login.test.ts", "line": 175, "column": 7}, {"title": "should display login form correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-a79981c52bb3afc5daa7", "file": "auth/login.test.ts", "line": 21, "column": 7}, {"title": "should successfully login with global test account", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-dd7b6d2997062253a8ba", "file": "auth/login.test.ts", "line": 37, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-34ec80a0a25758d579d6", "file": "auth/login.test.ts", "line": 55, "column": 7}, {"title": "should show error for non-existent user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-f0f58d4e705bf6b9f6a1", "file": "auth/login.test.ts", "line": 72, "column": 7}, {"title": "should handle form validation correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-8c50df37e252f3154af3", "file": "auth/login.test.ts", "line": 89, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-546e698da1226f037d50", "file": "auth/login.test.ts", "line": 110, "column": 7}, {"title": "should maintain form state during validation errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-99949ccf12a25c3f8f9e", "file": "auth/login.test.ts", "line": 121, "column": 7}, {"title": "should have working navigation links", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-feae9edbe7a92f45266c", "file": "auth/login.test.ts", "line": 138, "column": 7}, {"title": "should handle loading states correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-c5c2d836367326091246", "file": "auth/login.test.ts", "line": 151, "column": 7}, {"title": "should support keyboard navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-1062f16e7990e8427d36", "file": "auth/login.test.ts", "line": 175, "column": 7}, {"title": "should display login form correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-d8b8e3f955563d73edaa", "file": "auth/login.test.ts", "line": 21, "column": 7}, {"title": "should successfully login with global test account", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-6dca2e942d4ac2a66023", "file": "auth/login.test.ts", "line": 37, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-dbfa26e2daf7eee815ac", "file": "auth/login.test.ts", "line": 55, "column": 7}, {"title": "should show error for non-existent user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-cae1236761e6bb29976a", "file": "auth/login.test.ts", "line": 72, "column": 7}, {"title": "should handle form validation correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-f29385144b35c23e5de6", "file": "auth/login.test.ts", "line": 89, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-df4ce2451984a7b46eef", "file": "auth/login.test.ts", "line": 110, "column": 7}, {"title": "should maintain form state during validation errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-9864366310ec1c011e91", "file": "auth/login.test.ts", "line": 121, "column": 7}, {"title": "should have working navigation links", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-ae4d6557cc3e25203a43", "file": "auth/login.test.ts", "line": 138, "column": 7}, {"title": "should handle loading states correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-2ac5be9ac6a93d110210", "file": "auth/login.test.ts", "line": 151, "column": 7}, {"title": "should support keyboard navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-920288f2abc84cc22366", "file": "auth/login.test.ts", "line": 175, "column": 7}, {"title": "should display login form correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-0b85b0fe9956cb63644d", "file": "auth/login.test.ts", "line": 21, "column": 7}, {"title": "should successfully login with global test account", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-ba1bad3b4565aa24b29e", "file": "auth/login.test.ts", "line": 37, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-8dbf34d7ed2d8f70f0d5", "file": "auth/login.test.ts", "line": 55, "column": 7}, {"title": "should show error for non-existent user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-4281c7d5e651f80f5e03", "file": "auth/login.test.ts", "line": 72, "column": 7}, {"title": "should handle form validation correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-36ad756ce43c39f5c2fd", "file": "auth/login.test.ts", "line": 89, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-ca9aca3a0190baaef9a8", "file": "auth/login.test.ts", "line": 110, "column": 7}, {"title": "should maintain form state during validation errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-dd81927665f3e4a4f594", "file": "auth/login.test.ts", "line": 121, "column": 7}, {"title": "should have working navigation links", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-6c5e2bf81ef97009185f", "file": "auth/login.test.ts", "line": 138, "column": 7}, {"title": "should handle loading states correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-5c9311b58d5340b72bb9", "file": "auth/login.test.ts", "line": 151, "column": 7}, {"title": "should support keyboard navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-caa783ef5a15277fef36", "file": "auth/login.test.ts", "line": 175, "column": 7}, {"title": "should display login form correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-17b471b7b30ae3c4ae2c", "file": "auth/login.test.ts", "line": 21, "column": 7}, {"title": "should successfully login with global test account", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-533a87495ab76a8d9473", "file": "auth/login.test.ts", "line": 37, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-11d64f98ff2bd6aa66cd", "file": "auth/login.test.ts", "line": 55, "column": 7}, {"title": "should show error for non-existent user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-11b036992e9c0e190197", "file": "auth/login.test.ts", "line": 72, "column": 7}, {"title": "should handle form validation correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-c2fafeaa565dc32abb2b", "file": "auth/login.test.ts", "line": 89, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-3a342d6b314208329bd6", "file": "auth/login.test.ts", "line": 110, "column": 7}, {"title": "should maintain form state during validation errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-4cb4231348a0d49e79d6", "file": "auth/login.test.ts", "line": 121, "column": 7}, {"title": "should have working navigation links", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-366e7bf35e6da08d70a6", "file": "auth/login.test.ts", "line": 138, "column": 7}, {"title": "should handle loading states correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-7d9abe93de7853d0bf3c", "file": "auth/login.test.ts", "line": 151, "column": 7}, {"title": "should support keyboard navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-f3c8db97c67936b2db72", "file": "auth/login.test.ts", "line": 175, "column": 7}, {"title": "should display login form correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-75717b2959b01fbe244e", "file": "auth/login.test.ts", "line": 21, "column": 7}, {"title": "should successfully login with global test account", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-7e1eb67cbb24f0026e02", "file": "auth/login.test.ts", "line": 37, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-66e5b4a6b481a5ecec55", "file": "auth/login.test.ts", "line": 55, "column": 7}, {"title": "should show error for non-existent user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-271be0a6ebe0af1e36ed", "file": "auth/login.test.ts", "line": 72, "column": 7}, {"title": "should handle form validation correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-7c768c06a50bf5356da8", "file": "auth/login.test.ts", "line": 89, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-881ac2b49d5c945e4910", "file": "auth/login.test.ts", "line": 110, "column": 7}, {"title": "should maintain form state during validation errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-a2e90651bad4ad53ca09", "file": "auth/login.test.ts", "line": 121, "column": 7}, {"title": "should have working navigation links", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-289ff3bcd7ef5c31277d", "file": "auth/login.test.ts", "line": 138, "column": 7}, {"title": "should handle loading states correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-b0552829758274d43d41", "file": "auth/login.test.ts", "line": 151, "column": 7}, {"title": "should support keyboard navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "932ff77a7ef026530189-26999c14767dd2689181", "file": "auth/login.test.ts", "line": 175, "column": 7}]}]}, {"title": "auth/register.test.ts", "file": "auth/register.test.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Registration Flow", "file": "auth/register.test.ts", "line": 10, "column": 6, "specs": [{"title": "should display registration form correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-b821d32cd76348a8bf4a", "file": "auth/register.test.ts", "line": 21, "column": 7}, {"title": "should show validation error for password mismatch", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-2b21241a2f95ee082586", "file": "auth/register.test.ts", "line": 36, "column": 7}, {"title": "should successfully register new user with NextAuth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-b3ffbb9b0cd36c01ce9f", "file": "auth/register.test.ts", "line": 54, "column": 7}, {"title": "should show error for existing user email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-28eac206fe9230b6db2f", "file": "auth/register.test.ts", "line": 79, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-d6b11396ebd21353fbb0", "file": "auth/register.test.ts", "line": 98, "column": 7}, {"title": "should handle form validation correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-00dd5998712b8bcb5340", "file": "auth/register.test.ts", "line": 109, "column": 7}, {"title": "should maintain form state during validation errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-7ca5c47559778bbb956c", "file": "auth/register.test.ts", "line": 132, "column": 7}, {"title": "should have working navigation links", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-6e02df74e7a2a2c7a0d1", "file": "auth/register.test.ts", "line": 155, "column": 7}, {"title": "should display registration form correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-db6a4cf408d231d90c5f", "file": "auth/register.test.ts", "line": 21, "column": 7}, {"title": "should show validation error for password mismatch", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-7de8feed4529af3cf911", "file": "auth/register.test.ts", "line": 36, "column": 7}, {"title": "should successfully register new user with NextAuth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-9be9375e434f85970148", "file": "auth/register.test.ts", "line": 54, "column": 7}, {"title": "should show error for existing user email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-121e411a9edd657a8b17", "file": "auth/register.test.ts", "line": 79, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-0692b6b9ef8fd1de38c7", "file": "auth/register.test.ts", "line": 98, "column": 7}, {"title": "should handle form validation correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-0ecadfa81bafb35c76ca", "file": "auth/register.test.ts", "line": 109, "column": 7}, {"title": "should maintain form state during validation errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-22823904a0c9cd63ab6a", "file": "auth/register.test.ts", "line": 132, "column": 7}, {"title": "should have working navigation links", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-5019c9cf686e08f8ffd7", "file": "auth/register.test.ts", "line": 155, "column": 7}, {"title": "should display registration form correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-1367cff6810f874a7bce", "file": "auth/register.test.ts", "line": 21, "column": 7}, {"title": "should show validation error for password mismatch", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-a1ad9495380dc37a4f36", "file": "auth/register.test.ts", "line": 36, "column": 7}, {"title": "should successfully register new user with NextAuth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-80b87cf8d2012cc18258", "file": "auth/register.test.ts", "line": 54, "column": 7}, {"title": "should show error for existing user email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-da45ce93da8c3fc801b4", "file": "auth/register.test.ts", "line": 79, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-ea205a6ca0af91adf656", "file": "auth/register.test.ts", "line": 98, "column": 7}, {"title": "should handle form validation correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-cbe28ee024572bd19c85", "file": "auth/register.test.ts", "line": 109, "column": 7}, {"title": "should maintain form state during validation errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-af98a812eac313b24e0c", "file": "auth/register.test.ts", "line": 132, "column": 7}, {"title": "should have working navigation links", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-4db398862020a6c8939a", "file": "auth/register.test.ts", "line": 155, "column": 7}, {"title": "should display registration form correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-f5f822155c068af7b99d", "file": "auth/register.test.ts", "line": 21, "column": 7}, {"title": "should show validation error for password mismatch", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-446e38d7ff193946a8cb", "file": "auth/register.test.ts", "line": 36, "column": 7}, {"title": "should successfully register new user with NextAuth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-daf1c555c9052a9001cb", "file": "auth/register.test.ts", "line": 54, "column": 7}, {"title": "should show error for existing user email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-4574af1c17b9ae0db816", "file": "auth/register.test.ts", "line": 79, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-52fc9ee3701f5fb72e31", "file": "auth/register.test.ts", "line": 98, "column": 7}, {"title": "should handle form validation correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-8f84d648fcc8ac16339a", "file": "auth/register.test.ts", "line": 109, "column": 7}, {"title": "should maintain form state during validation errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-8d407b49720eb834b658", "file": "auth/register.test.ts", "line": 132, "column": 7}, {"title": "should have working navigation links", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-7dc9c0ac3c3cabeec215", "file": "auth/register.test.ts", "line": 155, "column": 7}, {"title": "should display registration form correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-95ac883e8fccae281dc7", "file": "auth/register.test.ts", "line": 21, "column": 7}, {"title": "should show validation error for password mismatch", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-76046ab78b3e898566d6", "file": "auth/register.test.ts", "line": 36, "column": 7}, {"title": "should successfully register new user with NextAuth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-8006d3d246caed946c26", "file": "auth/register.test.ts", "line": 54, "column": 7}, {"title": "should show error for existing user email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-e2e82cce8197eac8af9f", "file": "auth/register.test.ts", "line": 79, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-a4c45040e541c6d274fa", "file": "auth/register.test.ts", "line": 98, "column": 7}, {"title": "should handle form validation correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-63175303c379165a3f97", "file": "auth/register.test.ts", "line": 109, "column": 7}, {"title": "should maintain form state during validation errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-6c3da8db8d0acede206f", "file": "auth/register.test.ts", "line": 132, "column": 7}, {"title": "should have working navigation links", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-9eac3bac1c7a40860d7d", "file": "auth/register.test.ts", "line": 155, "column": 7}, {"title": "should display registration form correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-6ccaf979bed9ff297055", "file": "auth/register.test.ts", "line": 21, "column": 7}, {"title": "should show validation error for password mismatch", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-37e54488b49fd86108e3", "file": "auth/register.test.ts", "line": 36, "column": 7}, {"title": "should successfully register new user with NextAuth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-95093cb2c390f83f4f4b", "file": "auth/register.test.ts", "line": 54, "column": 7}, {"title": "should show error for existing user email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-b23799293e597d47b519", "file": "auth/register.test.ts", "line": 79, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-1d3a88f39f28f47c6fe2", "file": "auth/register.test.ts", "line": 98, "column": 7}, {"title": "should handle form validation correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-89918416f7eb8da0b68c", "file": "auth/register.test.ts", "line": 109, "column": 7}, {"title": "should maintain form state during validation errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-2db7d661bef6d598f008", "file": "auth/register.test.ts", "line": 132, "column": 7}, {"title": "should have working navigation links", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-8c423b09a9cbcce19962", "file": "auth/register.test.ts", "line": 155, "column": 7}, {"title": "should display registration form correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-cda504bcec8d4289f304", "file": "auth/register.test.ts", "line": 21, "column": 7}, {"title": "should show validation error for password mismatch", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-cbf6f481714cb7b50bb7", "file": "auth/register.test.ts", "line": 36, "column": 7}, {"title": "should successfully register new user with NextAuth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-5992aec31515fcda81f4", "file": "auth/register.test.ts", "line": 54, "column": 7}, {"title": "should show error for existing user email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-af36b9abb87034117ff0", "file": "auth/register.test.ts", "line": 79, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-25b7e65972c21ba7287c", "file": "auth/register.test.ts", "line": 98, "column": 7}, {"title": "should handle form validation correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-5eff42203b93f8d181ad", "file": "auth/register.test.ts", "line": 109, "column": 7}, {"title": "should maintain form state during validation errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-dd52ed171c62c3905e57", "file": "auth/register.test.ts", "line": 132, "column": 7}, {"title": "should have working navigation links", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "c85d8a829b770743c205-690352a278739ad768fb", "file": "auth/register.test.ts", "line": 155, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-05-25T20:32:18.635Z", "duration": 110.36300000000006, "expected": 0, "skipped": 126, "unexpected": 0, "flaky": 0}}