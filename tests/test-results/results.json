{"config": {"configFile": "/Users/<USER>/Desktop/adc/adc-account-web/tests/playwright.config.ts", "rootDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Desktop/adc/adc-account-web/tests/config/global-setup.ts", "globalTeardown": "/Users/<USER>/Desktop/adc/adc-account-web/tests/config/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 4, "webServer": null}, "suites": [{"title": "dashboard/dashboard-login-flow.test.ts", "file": "dashboard/dashboard-login-flow.test.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Dashboard Login Flow", "file": "dashboard/dashboard-login-flow.test.ts", "line": 15, "column": 6, "specs": [{"title": "Complete Login and Dashboard Flow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 22462, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Total Revenue').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Total Revenue').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Total Revenue').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Total Revenue').first()\u001b[22m\n\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:102:31", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 31, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m     \u001b[90m// Check for financial summary cards\u001b[39m\n \u001b[90m 101 |\u001b[39m     \u001b[36mconst\u001b[39m revenueCard \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Total Revenue'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m     \u001b[36mawait\u001b[39m expect(revenueCard)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Revenue card visible'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m     \u001b[36mconst\u001b[39m expensesCard \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Total Expenses'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 31, "line": 102}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Total Revenue').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Total Revenue').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 100 |\u001b[39m     \u001b[90m// Check for financial summary cards\u001b[39m\n \u001b[90m 101 |\u001b[39m     \u001b[36mconst\u001b[39m revenueCard \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Total Revenue'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m     \u001b[36mawait\u001b[39m expect(revenueCard)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Revenue card visible'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m     \u001b[36mconst\u001b[39m expensesCard \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Total Expenses'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:102:31\u001b[22m"}], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}, {"text": "🚀 Starting complete login and dashboard flow test...\n"}, {"text": "📍 Step 1: Navigating to login page...\n"}, {"text": "📸 Screenshot taken: 01-login-page.png\n"}, {"text": "📍 Step 2: Filling login form...\n"}, {"text": "Using test account: <EMAIL>\n"}, {"text": "📸 Screenshot taken: 02-login-form-filled.png\n"}, {"text": "📍 Step 3: Submitting login form...\n"}, {"text": "⏳ Waiting for redirect to dashboard...\n"}, {"text": "✅ Successfully redirected to dashboard!\n"}, {"text": "📍 Step 4: Verifying dashboard elements...\n"}, {"text": "✅ Dashboard title verified\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:31:16.221Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 31, "line": 102}}], "status": "unexpected"}], "id": "d1203e22302afd0a7381-935a6afc8325dd040761", "file": "dashboard/dashboard-login-flow.test.ts", "line": 41, "column": 7}, {"title": "Dashboard Data Loading and Error Handling", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 15955, "errors": [], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}, {"text": "🚀 Starting dashboard data loading test...\n"}, {"text": "📍 Testing loading states...\n"}, {"text": "✅ No error messages found\n"}, {"text": "🎉 Dashboard data loading test completed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:31:16.218Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d1203e22302afd0a7381-8f47fcafb4ff805fe9d3", "file": "dashboard/dashboard-login-flow.test.ts", "line": 206, "column": 7}, {"title": "Complete Login and Dashboard Flow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 21870, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Total Revenue').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Total Revenue').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Total Revenue').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Total Revenue').first()\u001b[22m\n\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:102:31", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 31, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m     \u001b[90m// Check for financial summary cards\u001b[39m\n \u001b[90m 101 |\u001b[39m     \u001b[36mconst\u001b[39m revenueCard \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Total Revenue'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m     \u001b[36mawait\u001b[39m expect(revenueCard)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Revenue card visible'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m     \u001b[36mconst\u001b[39m expensesCard \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Total Expenses'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 31, "line": 102}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Total Revenue').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Total Revenue').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 100 |\u001b[39m     \u001b[90m// Check for financial summary cards\u001b[39m\n \u001b[90m 101 |\u001b[39m     \u001b[36mconst\u001b[39m revenueCard \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Total Revenue'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m     \u001b[36mawait\u001b[39m expect(revenueCard)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Revenue card visible'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m     \u001b[36mconst\u001b[39m expensesCard \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Total Expenses'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:102:31\u001b[22m"}], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}, {"text": "🚀 Starting complete login and dashboard flow test...\n"}, {"text": "📍 Step 1: Navigating to login page...\n"}, {"text": "📸 Screenshot taken: 01-login-page.png\n"}, {"text": "📍 Step 2: Filling login form...\n"}, {"text": "Using test account: <EMAIL>\n"}, {"text": "📸 Screenshot taken: 02-login-form-filled.png\n"}, {"text": "📍 Step 3: Submitting login form...\n"}, {"text": "⏳ Waiting for redirect to dashboard...\n"}, {"text": "✅ Successfully redirected to dashboard!\n"}, {"text": "📍 Step 4: Verifying dashboard elements...\n"}, {"text": "✅ Dashboard title verified\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:31:16.219Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-firefox/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 31, "line": 102}}], "status": "unexpected"}], "id": "d1203e22302afd0a7381-2b1252ec3634eb1e8748", "file": "dashboard/dashboard-login-flow.test.ts", "line": 41, "column": 7}, {"title": "Dashboard Data Loading and Error Handling", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 16872, "errors": [], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}, {"text": "🚀 Starting dashboard data loading test...\n"}, {"text": "📍 Testing loading states...\n"}, {"text": "✅ No error messages found\n"}, {"text": "🎉 Dashboard data loading test completed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:31:16.219Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d1203e22302afd0a7381-188cdcb555513d7429e8", "file": "dashboard/dashboard-login-flow.test.ts", "line": 206, "column": 7}, {"title": "Complete Login and Dashboard Flow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "failed", "duration": 14012, "error": {"message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================", "stack": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:83:16", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 83}, "snippet": "\u001b[0m \u001b[90m 81 |\u001b[39m     \u001b[90m// Wait for navigation to dashboard\u001b[39m\n \u001b[90m 82 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'⏳ Waiting for redirect to dashboard...'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 83 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m\u001b[33m,\u001b[39m { \n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 84 |\u001b[39m       timeout\u001b[33m:\u001b[39m \u001b[33mTEST_CONFIG\u001b[39m\u001b[33m.\u001b[39m\u001b[33mNAVIGATION_TIMEOUT\u001b[39m \n \u001b[90m 85 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 86 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 83}, "message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n\n\u001b[0m \u001b[90m 81 |\u001b[39m     \u001b[90m// Wait for navigation to dashboard\u001b[39m\n \u001b[90m 82 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'⏳ Waiting for redirect to dashboard...'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 83 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m\u001b[33m,\u001b[39m { \n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 84 |\u001b[39m       timeout\u001b[33m:\u001b[39m \u001b[33mTEST_CONFIG\u001b[39m\u001b[33m.\u001b[39m\u001b[33mNAVIGATION_TIMEOUT\u001b[39m \n \u001b[90m 85 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 86 |\u001b[39m     \u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:83:16\u001b[22m"}], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}, {"text": "🚀 Starting complete login and dashboard flow test...\n"}, {"text": "📍 Step 1: Navigating to login page...\n"}, {"text": "📸 Screenshot taken: 01-login-page.png\n"}, {"text": "📍 Step 2: Filling login form...\n"}, {"text": "Using test account: <EMAIL>\n"}, {"text": "📸 Screenshot taken: 02-login-form-filled.png\n"}, {"text": "📍 Step 3: Submitting login form...\n"}, {"text": "⏳ Waiting for redirect to dashboard...\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:31:38.311Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-webkit/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 83}}], "status": "unexpected"}], "id": "d1203e22302afd0a7381-ffccbe61cc7a9b38b30f", "file": "dashboard/dashboard-login-flow.test.ts", "line": 41, "column": 7}, {"title": "Dashboard Data Loading and Error Handling", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 5, "parallelIndex": 3, "status": "failed", "duration": 12931, "error": {"message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================", "stack": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:214:16", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 214}, "snippet": "\u001b[0m \u001b[90m 212 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mGLOBAL_TEST_ACCOUNT\u001b[39m\u001b[33m.\u001b[39mpassword)\u001b[33m;\u001b[39m\n \u001b[90m 213 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 214 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 215 |\u001b[39m\n \u001b[90m 216 |\u001b[39m     \u001b[90m// Test loading states and error handling\u001b[39m\n \u001b[90m 217 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'📍 Testing loading states...'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 214}, "message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n\n\u001b[0m \u001b[90m 212 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mGLOBAL_TEST_ACCOUNT\u001b[39m\u001b[33m.\u001b[39mpassword)\u001b[33m;\u001b[39m\n \u001b[90m 213 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 214 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 215 |\u001b[39m\n \u001b[90m 216 |\u001b[39m     \u001b[90m// Test loading states and error handling\u001b[39m\n \u001b[90m 217 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'📍 Testing loading states...'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:214:16\u001b[22m"}], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}, {"text": "🚀 Starting dashboard data loading test...\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:31:38.633Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--2ca08--Loading-and-Error-Handling-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--2ca08--Loading-and-Error-Handling-webkit/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--2ca08--Loading-and-Error-Handling-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 214}}], "status": "unexpected"}], "id": "d1203e22302afd0a7381-7fa0db6ebe53d49fef7c", "file": "dashboard/dashboard-login-flow.test.ts", "line": 206, "column": 7}, {"title": "Complete Login and Dashboard Flow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "failed", "duration": 13079, "error": {"message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================", "stack": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:83:16", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 83}, "snippet": "\u001b[0m \u001b[90m 81 |\u001b[39m     \u001b[90m// Wait for navigation to dashboard\u001b[39m\n \u001b[90m 82 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'⏳ Waiting for redirect to dashboard...'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 83 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m\u001b[33m,\u001b[39m { \n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 84 |\u001b[39m       timeout\u001b[33m:\u001b[39m \u001b[33mTEST_CONFIG\u001b[39m\u001b[33m.\u001b[39m\u001b[33mNAVIGATION_TIMEOUT\u001b[39m \n \u001b[90m 85 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 86 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 83}, "message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n\n\u001b[0m \u001b[90m 81 |\u001b[39m     \u001b[90m// Wait for navigation to dashboard\u001b[39m\n \u001b[90m 82 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'⏳ Waiting for redirect to dashboard...'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 83 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m\u001b[33m,\u001b[39m { \n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 84 |\u001b[39m       timeout\u001b[33m:\u001b[39m \u001b[33mTEST_CONFIG\u001b[39m\u001b[33m.\u001b[39m\u001b[33mNAVIGATION_TIMEOUT\u001b[39m \n \u001b[90m 85 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 86 |\u001b[39m     \u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:83:16\u001b[22m"}], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}, {"text": "🚀 Starting complete login and dashboard flow test...\n"}, {"text": "📍 Step 1: Navigating to login page...\n"}, {"text": "📸 Screenshot taken: 01-login-page.png\n"}, {"text": "📍 Step 2: Filling login form...\n"}, {"text": "Using test account: <EMAIL>\n"}, {"text": "📸 Screenshot taken: 02-login-form-filled.png\n"}, {"text": "📍 Step 3: Submitting login form...\n"}, {"text": "⏳ Waiting for redirect to dashboard...\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:31:44.776Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-Mobile-Chrome/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 83}}], "status": "unexpected"}], "id": "d1203e22302afd0a7381-be4f54f4b514d4e1da54", "file": "dashboard/dashboard-login-flow.test.ts", "line": 41, "column": 7}, {"title": "Dashboard Data Loading and Error Handling", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 7, "parallelIndex": 2, "status": "failed", "duration": 12114, "error": {"message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================", "stack": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:214:16", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 214}, "snippet": "\u001b[0m \u001b[90m 212 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mGLOBAL_TEST_ACCOUNT\u001b[39m\u001b[33m.\u001b[39mpassword)\u001b[33m;\u001b[39m\n \u001b[90m 213 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 214 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 215 |\u001b[39m\n \u001b[90m 216 |\u001b[39m     \u001b[90m// Test loading states and error handling\u001b[39m\n \u001b[90m 217 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'📍 Testing loading states...'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 214}, "message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n\n\u001b[0m \u001b[90m 212 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mGLOBAL_TEST_ACCOUNT\u001b[39m\u001b[33m.\u001b[39mpassword)\u001b[33m;\u001b[39m\n \u001b[90m 213 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 214 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 215 |\u001b[39m\n \u001b[90m 216 |\u001b[39m     \u001b[90m// Test loading states and error handling\u001b[39m\n \u001b[90m 217 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'📍 Testing loading states...'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:214:16\u001b[22m"}], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}, {"text": "🚀 Starting dashboard data loading test...\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:31:45.409Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--2ca08--Loading-and-Error-Handling-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--2ca08--Loading-and-Error-Handling-Mobile-Chrome/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--2ca08--Loading-and-Error-Handling-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 214}}], "status": "unexpected"}], "id": "d1203e22302afd0a7381-a5963787e9d8ad200e7a", "file": "dashboard/dashboard-login-flow.test.ts", "line": 206, "column": 7}, {"title": "Complete Login and Dashboard Flow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 8, "parallelIndex": 3, "status": "failed", "duration": 12651, "error": {"message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================", "stack": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:83:16", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 83}, "snippet": "\u001b[0m \u001b[90m 81 |\u001b[39m     \u001b[90m// Wait for navigation to dashboard\u001b[39m\n \u001b[90m 82 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'⏳ Waiting for redirect to dashboard...'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 83 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m\u001b[33m,\u001b[39m { \n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 84 |\u001b[39m       timeout\u001b[33m:\u001b[39m \u001b[33mTEST_CONFIG\u001b[39m\u001b[33m.\u001b[39m\u001b[33mNAVIGATION_TIMEOUT\u001b[39m \n \u001b[90m 85 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 86 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 83}, "message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n\n\u001b[0m \u001b[90m 81 |\u001b[39m     \u001b[90m// Wait for navigation to dashboard\u001b[39m\n \u001b[90m 82 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'⏳ Waiting for redirect to dashboard...'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 83 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m\u001b[33m,\u001b[39m { \n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 84 |\u001b[39m       timeout\u001b[33m:\u001b[39m \u001b[33mTEST_CONFIG\u001b[39m\u001b[33m.\u001b[39m\u001b[33mNAVIGATION_TIMEOUT\u001b[39m \n \u001b[90m 85 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 86 |\u001b[39m     \u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:83:16\u001b[22m"}], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}, {"text": "🚀 Starting complete login and dashboard flow test...\n"}, {"text": "📍 Step 1: Navigating to login page...\n"}, {"text": "📸 Screenshot taken: 01-login-page.png\n"}, {"text": "📍 Step 2: Filling login form...\n"}, {"text": "Using test account: <EMAIL>\n"}, {"text": "📸 Screenshot taken: 02-login-form-filled.png\n"}, {"text": "📍 Step 3: Submitting login form...\n"}, {"text": "⏳ Waiting for redirect to dashboard...\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:31:52.404Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-Mobile-Safari/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 83}}], "status": "unexpected"}], "id": "d1203e22302afd0a7381-1d7d232f09ef8ef9ecb3", "file": "dashboard/dashboard-login-flow.test.ts", "line": 41, "column": 7}, {"title": "Dashboard Data Loading and Error Handling", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 9, "parallelIndex": 1, "status": "failed", "duration": 11926, "error": {"message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================", "stack": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:214:16", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 214}, "snippet": "\u001b[0m \u001b[90m 212 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mGLOBAL_TEST_ACCOUNT\u001b[39m\u001b[33m.\u001b[39mpassword)\u001b[33m;\u001b[39m\n \u001b[90m 213 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 214 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 215 |\u001b[39m\n \u001b[90m 216 |\u001b[39m     \u001b[90m// Test loading states and error handling\u001b[39m\n \u001b[90m 217 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'📍 Testing loading states...'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 214}, "message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n\n\u001b[0m \u001b[90m 212 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mGLOBAL_TEST_ACCOUNT\u001b[39m\u001b[33m.\u001b[39mpassword)\u001b[33m;\u001b[39m\n \u001b[90m 213 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 214 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 215 |\u001b[39m\n \u001b[90m 216 |\u001b[39m     \u001b[90m// Test loading states and error handling\u001b[39m\n \u001b[90m 217 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'📍 Testing loading states...'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:214:16\u001b[22m"}], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}, {"text": "🚀 Starting dashboard data loading test...\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:31:54.181Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--2ca08--Loading-and-Error-Handling-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--2ca08--Loading-and-Error-Handling-Mobile-Safari/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--2ca08--Loading-and-Error-Handling-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 214}}], "status": "unexpected"}], "id": "d1203e22302afd0a7381-290f3328d41bb25c5b6e", "file": "dashboard/dashboard-login-flow.test.ts", "line": 206, "column": 7}, {"title": "Complete Login and Dashboard Flow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 10, "parallelIndex": 2, "status": "failed", "duration": 2, "error": {"message": "Error: browserType.launch: Chromium distribution 'msedge' is not found at /Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge\nRun \"npx playwright install msedge\"", "stack": "Error: browserType.launch: Chromium distribution 'msedge' is not found at /Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge\nRun \"npx playwright install msedge\""}, "errors": [{"message": "Error: browserType.launch: Chromium distribution 'msedge' is not found at /Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge\nRun \"npx playwright install msedge\""}], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:31:59.126Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-Microsoft-Edge/error-context.md"}]}], "status": "unexpected"}], "id": "d1203e22302afd0a7381-38574484472bf4ffee63", "file": "dashboard/dashboard-login-flow.test.ts", "line": 41, "column": 7}, {"title": "Dashboard Data Loading and Error Handling", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 11, "parallelIndex": 0, "status": "failed", "duration": 2, "error": {"message": "Error: browserType.launch: Chromium distribution 'msedge' is not found at /Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge\nRun \"npx playwright install msedge\"", "stack": "Error: browserType.launch: Chromium distribution 'msedge' is not found at /Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge\nRun \"npx playwright install msedge\""}, "errors": [{"message": "Error: browserType.launch: Chromium distribution 'msedge' is not found at /Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge\nRun \"npx playwright install msedge\""}], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:31:59.257Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--2ca08--Loading-and-Error-Handling-Microsoft-Edge/error-context.md"}]}], "status": "unexpected"}], "id": "d1203e22302afd0a7381-a67c42e263864a30c35b", "file": "dashboard/dashboard-login-flow.test.ts", "line": 206, "column": 7}, {"title": "Complete Login and Dashboard Flow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 12, "parallelIndex": 2, "status": "failed", "duration": 14716, "error": {"message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================", "stack": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:83:16", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 83}, "snippet": "\u001b[0m \u001b[90m 81 |\u001b[39m     \u001b[90m// Wait for navigation to dashboard\u001b[39m\n \u001b[90m 82 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'⏳ Waiting for redirect to dashboard...'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 83 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m\u001b[33m,\u001b[39m { \n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 84 |\u001b[39m       timeout\u001b[33m:\u001b[39m \u001b[33mTEST_CONFIG\u001b[39m\u001b[33m.\u001b[39m\u001b[33mNAVIGATION_TIMEOUT\u001b[39m \n \u001b[90m 85 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 86 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 83}, "message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n\n\u001b[0m \u001b[90m 81 |\u001b[39m     \u001b[90m// Wait for navigation to dashboard\u001b[39m\n \u001b[90m 82 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'⏳ Waiting for redirect to dashboard...'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 83 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m\u001b[33m,\u001b[39m { \n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 84 |\u001b[39m       timeout\u001b[33m:\u001b[39m \u001b[33mTEST_CONFIG\u001b[39m\u001b[33m.\u001b[39m\u001b[33mNAVIGATION_TIMEOUT\u001b[39m \n \u001b[90m 85 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 86 |\u001b[39m     \u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:83:16\u001b[22m"}], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}, {"text": "🚀 Starting complete login and dashboard flow test...\n"}, {"text": "📍 Step 1: Navigating to login page...\n"}, {"text": "📸 Screenshot taken: 01-login-page.png\n"}, {"text": "📍 Step 2: Filling login form...\n"}, {"text": "Using test account: <EMAIL>\n"}, {"text": "📸 Screenshot taken: 02-login-form-filled.png\n"}, {"text": "📍 Step 3: Submitting login form...\n"}, {"text": "⏳ Waiting for redirect to dashboard...\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:32:00.627Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-Google-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-Google-Chrome/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--32eec-te-Login-and-Dashboard-Flow-Google-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 83}}], "status": "unexpected"}], "id": "d1203e22302afd0a7381-b20c0c43a2f6e2dfa5e7", "file": "dashboard/dashboard-login-flow.test.ts", "line": 41, "column": 7}, {"title": "Dashboard Data Loading and Error Handling", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 13, "parallelIndex": 0, "status": "failed", "duration": 13948, "error": {"message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================", "stack": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:214:16", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 214}, "snippet": "\u001b[0m \u001b[90m 212 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mGLOBAL_TEST_ACCOUNT\u001b[39m\u001b[33m.\u001b[39mpassword)\u001b[33m;\u001b[39m\n \u001b[90m 213 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 214 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 215 |\u001b[39m\n \u001b[90m 216 |\u001b[39m     \u001b[90m// Test loading states and error handling\u001b[39m\n \u001b[90m 217 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'📍 Testing loading states...'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 214}, "message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n\n\u001b[0m \u001b[90m 212 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mGLOBAL_TEST_ACCOUNT\u001b[39m\u001b[33m.\u001b[39mpassword)\u001b[33m;\u001b[39m\n \u001b[90m 213 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 214 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 215 |\u001b[39m\n \u001b[90m 216 |\u001b[39m     \u001b[90m// Test loading states and error handling\u001b[39m\n \u001b[90m 217 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'📍 Testing loading states...'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts:214:16\u001b[22m"}], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}, {"text": "🚀 Starting dashboard data loading test...\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:32:02.420Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--2ca08--Loading-and-Error-Handling-Google-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--2ca08--Loading-and-Error-Handling-Google-Chrome/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/dashboard-dashboard-login--2ca08--Loading-and-Error-Handling-Google-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/dashboard/dashboard-login-flow.test.ts", "column": 16, "line": 214}}], "status": "unexpected"}], "id": "d1203e22302afd0a7381-19db043388be9fa66827", "file": "dashboard/dashboard-login-flow.test.ts", "line": 206, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-05-26T03:31:10.299Z", "duration": 67320.223, "expected": 2, "skipped": 0, "unexpected": 12, "flaky": 0}}