{"config": {"configFile": "/Users/<USER>/Desktop/adc/adc-account-web/tests/playwright.config.ts", "rootDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Desktop/adc/adc-account-web/tests/config/global-setup.ts", "globalTeardown": "/Users/<USER>/Desktop/adc/adc-account-web/tests/config/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 4, "webServer": null}, "suites": [{"title": "dashboard/dashboard-login-flow.test.ts", "file": "dashboard/dashboard-login-flow.test.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Dashboard Login Flow", "file": "dashboard/dashboard-login-flow.test.ts", "line": 15, "column": 6, "specs": [{"title": "Complete Login and Dashboard Flow", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 6168, "errors": [], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}, {"text": "🚀 Starting complete login and dashboard flow test...\n"}, {"text": "📍 Step 1: Navigating to login page...\n"}, {"text": "📸 Screenshot taken: 01-login-page.png\n"}, {"text": "📍 Step 2: Filling login form...\n"}, {"text": "Using test account: <EMAIL>\n"}, {"text": "📸 Screenshot taken: 02-login-form-filled.png\n"}, {"text": "📍 Step 3: Submitting login form...\n"}, {"text": "📸 Screenshot taken: 03-after-submit.png\n"}, {"text": "Current URL after submission: http://localhost:3000/en/dashboard\n"}, {"text": "✅ Successfully redirected to dashboard!\n"}, {"text": "✅ Successfully navigated to dashboard!\n"}, {"text": "📍 Step 4: Verifying dashboard elements...\n"}, {"text": "✅ Dashboard title verified\n"}, {"text": "📍 Looking for dashboard content...\n"}, {"text": "📸 Screenshot taken: 03-dashboard-loaded.png\n"}, {"text": "ℹ️ No specific dashboard content selectors found, checking for general content...\n"}, {"text": "✅ Found 19 card elements\n"}, {"text": "📍 Step 5: Testing basic dashboard functionality...\n"}, {"text": "ℹ️ Found 41 buttons on the page\n"}, {"text": "✅ Navigation elements found\n"}, {"text": "📸 Screenshot taken: 04-dashboard-final.png\n"}, {"text": "📍 Step 6: Verifying authentication state...\n"}, {"text": "Current URL: http://localhost:3000/en/dashboard\n"}, {"text": "✅ Still on dashboard page - authentication appears successful\n"}, {"text": "✅ User-related elements found\n"}, {"text": "🎉 Complete dashboard login flow test completed successfully!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:58:41.082Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d1203e22302afd0a7381-935a6afc8325dd040761", "file": "dashboard/dashboard-login-flow.test.ts", "line": 41, "column": 7}, {"title": "Dashboard Data Loading and Error Handling", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 3934, "errors": [], "stdout": [{"text": "🔍 Verifying servers are running...\n"}, {"text": "✅ Frontend server status: 200\n"}, {"text": "✅ Backend server status: 200\n"}, {"text": "🎉 Both servers are running successfully!\n"}, {"text": "🚀 Starting dashboard data loading test...\n"}, {"text": "📍 Testing loading states...\n"}, {"text": "✅ No error messages found\n"}, {"text": "🎉 Dashboard data loading test completed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-26T03:58:41.082Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d1203e22302afd0a7381-8f47fcafb4ff805fe9d3", "file": "dashboard/dashboard-login-flow.test.ts", "line": 204, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-05-26T03:58:33.127Z", "duration": 15277.805, "expected": 2, "skipped": 0, "unexpected": 0, "flaky": 0}}