{"config": {"configFile": "/Users/<USER>/Desktop/adc/adc-account-web/playwright.config.ts", "rootDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/e2e-results.json"}], ["junit", {"outputFile": "test-results/e2e-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [], "errors": [{"message": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments.", "stack": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments."}], "stats": {"startTime": "2025-05-25T20:31:25.869Z", "duration": 2677.664, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}